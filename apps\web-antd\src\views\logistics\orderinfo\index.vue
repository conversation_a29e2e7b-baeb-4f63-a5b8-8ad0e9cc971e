<template>
  <Page auto-content-height>
    <FormModal @success="onRefresh" />
    <PrintModal />
    <Grid table-title="物流订单入仓列表">
      <template #toolbar-tools>
        <TableAction :actions="toolbarActions" />
      </template>
      <template #actions="{ row }">
        <TableAction :actions="rowActions(row)" />
      </template>
    </Grid>
  </Page>
</template>
<script lang="ts" setup>
import type { VxeTableGridOptions } from '#/adapter/vxe-table'
import type { OrderInfoApi } from '#/api/logistics/orderinfo'
import { ref, nextTick } from 'vue'
import { Page, useVbenModal } from '@vben/common-ui'
import { downloadFileFromBlobPart, isEmpty } from '@vben/utils'
import { message } from 'ant-design-vue'
import { ACTION_ICON, TableAction, useVbenVxeGrid } from '#/adapter/vxe-table'
import { getOrderInfoPage, deleteOrderInfo, exportOrderInfo } from '#/api/logistics/orderinfo'
import { $t } from '#/locales'
import { useGridColumns, useGridFormSchema } from './data'
import Form from './OrderInfoForm.vue'
import PrintTemplate from './PrintTemplate.vue'
import type { ActionItem } from '#/components/table-action/typing'

const [FormModal, formModalApi] = useVbenModal({
  connectedComponent: Form,
  destroyOnClose: true,
})

const [PrintModal, printModalApi] = useVbenModal({
  connectedComponent: PrintTemplate,
  destroyOnClose: true,
  modalProps: {
    title: '打印预览',
    width: '90%',
    style: { top: '20px' },
  },
})

function onRefresh() {
  gridApi.query()
}

function handleCreate() {
  formModalApi.setData({}).open()
}

function handleEdit(row: OrderInfoApi.OrderInfoVO) {
  formModalApi.setData(row).open()
}

async function handleDelete(row: OrderInfoApi.OrderInfoVO) {
  const hideLoading = message.loading({
    content: $t('ui.actionMessage.deleting', [row.id]),
    duration: 0,
    key: 'action_process_msg',
  })
  try {
    await deleteOrderInfo(row.id as number)
    message.success($t('ui.actionMessage.deleteSuccess', [row.id]))
    onRefresh()
  } finally {
    hideLoading()
  }
}

async function handleDeleteBatch() {
  const hideLoading = message.loading({
    content: $t('ui.actionMessage.deleting'),
    duration: 0,
    key: 'action_process_msg',
  })
  try {
    for (const id of checkedIds.value) {
      await deleteOrderInfo(id)
    }
    message.success($t('ui.actionMessage.deleteSuccess'))
    onRefresh()
  } finally {
    hideLoading()
  }
}

const checkedIds = ref<number[]>([])
function handleRowCheckboxChange({ records }: { records: OrderInfoApi.OrderInfoVO[] }) {
  checkedIds.value = records.map((item) => item.id)
}

async function handleExport() {
  const data = await exportOrderInfo(await gridApi.formApi.getValues())
  downloadFileFromBlobPart({ fileName: '物流订单入仓.xls', source: data })
}

function handlePrint(row: OrderInfoApi.OrderInfoVO) {
  // 模拟订单明细数据，实际项目中应该从API获取
  const orderItems = [
    { itemName: '商品1', quantity: 10 },
    { itemName: '商品2', quantity: 5 },
    { itemName: '商品3', quantity: 8 },
  ]

  printModalApi.setData({
    logo: '/logo.png', // 公司logo路径，需要根据实际情况调整
    orderInfo: row,
    orderItems: orderItems,
  }).open()

  // 延迟执行打印，等待模态框完全渲染
  nextTick(() => {
    setTimeout(() => {
      window.print()
    }, 500)
  })
}

const toolbarActions: ActionItem[] = [
  {
    label: $t('ui.actionTitle.create', ['物流订单入仓']),
    type: 'primary',
    icon: ACTION_ICON.ADD,
    auth: ['logistics:order-info:create'],
    onClick: handleCreate,
  },
  {
    label: $t('ui.actionTitle.export'),
    type: 'primary',
    icon: ACTION_ICON.DOWNLOAD,
    auth: ['logistics:order-info:export'],
    onClick: handleExport,
  },
  {
    label: '批量删除',
    type: 'primary',
    danger: true,
    disabled: isEmpty(checkedIds),
    icon: ACTION_ICON.DELETE,
    auth: ['logistics:order-info:delete'],
    onClick: handleDeleteBatch,
  },
]

function rowActions(row: OrderInfoApi.OrderInfoVO): ActionItem[] {
  return [
    {
      label: $t('common.edit'),
      type: 'link',
      icon: ACTION_ICON.EDIT,
      auth: ['logistics:order-info:update'],
      onClick: handleEdit.bind(null, row),
    },
    {
      label: '打印',
      type: 'link',
      icon: 'lucide:printer',
      auth: ['logistics:order-info:print'],
      onClick: handlePrint.bind(null, row),
    },
    {
      label: $t('common.delete'),
      type: 'link',
      danger: true,
      icon: ACTION_ICON.DELETE,
      auth: ['logistics:order-info:delete'],
      popConfirm: {
        title: $t('ui.actionMessage.deleteConfirm', [row.orderNo]),
        confirm: handleDelete.bind(null, row),
      },
    },
  ]
}

const [Grid, gridApi] = useVbenVxeGrid({
  formOptions: {
    schema: useGridFormSchema(),
  },
  gridOptions: {
    columns: useGridColumns(),
    height: 'auto',
    pagerConfig: { enabled: true },
    proxyConfig: {
      ajax: {
        query: async ({ page }, formValues) => {
          return await getOrderInfoPage({
            pageNo: page.currentPage,
            pageSize: page.pageSize,
            ...formValues,
          })
        },
      },
    },
    rowConfig: { keyField: 'id', isHover: true },
    toolbarConfig: { refresh: { code: 'query' }, search: true },
  } as VxeTableGridOptions<OrderInfoApi.OrderInfoVO>,
  gridEvents: {
    checkboxAll: handleRowCheckboxChange,
    checkboxChange: handleRowCheckboxChange,
  },
})
</script>
