import ExcelJS from 'exceljs';

// 定义数据接口
export interface DeliveryNoteData {
  serialNumber?: string;
  logoUrl?: string; // 动态logo URL
  formData: {
    markNumber?: string;
    orderNo?: string;
    quantity?: number;
    shippingMethod?: string;
  };
}

/**
 * 生成条形码文本表示
 */
function generateBarcodeText(text: string): string {
  // 简单的条形码文本表示
  return `|||${text
    .split('')
    .map(() => '||')
    .join('|')}|||`;
}

/**
 * 生成运输方式文本
 */
function generateShippingMethodText(shippingMethod?: number): string {
  if (shippingMethod === 2) {
    return '●SEA海运\n○AIR空运';
  } else if (shippingMethod === 1) {
    return '○SEA海运\n●AIR空运';
  } else {
    return '○SEA海运\n○AIR空运';
  }
}

/**
 * 使用模板填充数据生成Excel文件
 */
export async function fillDeliveryNoteTemplate(
  data: DeliveryNoteData,
): Promise<ExcelJS.Buffer> {
  // 加载模板文件
  const templatePath = '/templates/delivery-note-template.xlsx';
  const response = await fetch(templatePath);

  if (!response.ok) {
    throw new Error(`无法加载模板文件: ${templatePath}`);
  }

  const arrayBuffer = await response.arrayBuffer();
  const workbook = new ExcelJS.Workbook();
  await workbook.xlsx.load(arrayBuffer);

  const worksheet = workbook.getWorksheet('Delivery Note');
  if (!worksheet) {
    throw new Error('模板文件中未找到 "Delivery Note" 工作表');
  }

  // 替换占位符数据
  const replacements = {
    '{{SERIAL_NUMBER}}': data.serialNumber || '679',
    '{{MARK_NUMBER}}': data.formData.markNumber || '',
    '{{SHIPPING_METHOD}}': generateShippingMethodText(
      data.formData.shippingMethod,
    ),
    '{{BARCODE}}': generateBarcodeText(data.formData.markNumber || ''),
    '{{ORDER_NO}}': data.formData.orderNo || '',
    '{{QR_CODE}}': '■■■■■■■■■\n■■■■■■■■■\n■■■■■■■■■',
    '{{QUANTITY}}': (data.formData.quantity || 0).toString(),
    '{{BIG_MARK_NUMBER}}': data.formData.markNumber || '',
  };

  // 遍历所有单元格，替换占位符
  worksheet.eachRow((row) => {
    row.eachCell((cell) => {
      if (cell.value && typeof cell.value === 'string') {
        let cellValue = cell.value;
        for (const [placeholder, replacement] of Object.entries(replacements)) {
          if (cellValue.includes(placeholder)) {
            cellValue = cellValue.replace(placeholder, replacement);
          }
        }
        if (cellValue !== cell.value) {
          cell.value = cellValue;
        }
      }
    });
  });

  return workbook.xlsx.writeBuffer() as Promise<ExcelJS.Buffer>;
}

/**
 * 为了向后兼容保留的函数
 */
export async function createDeliveryNoteTemplate(
  data: DeliveryNoteData,
): Promise<ExcelJS.Buffer> {
  return fillDeliveryNoteTemplate(data);
}

/**
 * 将填充数据的Excel模板转换为HTML格式
 */
export async function convertExcelTemplateToHTML(
  data: DeliveryNoteData,
): Promise<string> {
  // 生成条形码和二维码图片
  const barcodeImage = await generateBarcodeImage(
    data.formData.markNumber || '',
  );
  const qrCodeImage = await generateQRCodeImage(data.formData.orderNo || '');

  // 替换占位符数据
  const replacements = {
    '{{SERIAL_NUMBER}}': data.serialNumber || '679',
    '{{ORDER_NO}}': data.formData.orderNo || '',
    '{{MARK_NUMBER}}': data.formData.markNumber || '',
    '{{SHIPPING_METHOD}}': generateShippingMethodText(
      data.formData.shippingMethod,
    ),
    '{{LOGO_URL}}': data.logoUrl || '', // 动态logo
    '{{BARCODE}}': barcodeImage
      ? `<img src="${barcodeImage}" alt="Barcode" style="max-width: 100%; height: auto;" />`
      : generateBarcodeText(data.formData.markNumber || ''),
    '{{QR_CODE}}': qrCodeImage
      ? `<img src="${qrCodeImage}" alt="QR Code" style="width: 80px; height: 80px;" />`
      : '■■■■■■■■■<br>■■■■■■■■■<br>■■■■■■■■■',
    '{{QUANTITY}}': (data.formData.quantity || 0).toString(),
    '{{BIG_MARK_NUMBER}}': data.formData.markNumber || '',
  };

  // 转换为HTML
  return generateHTMLFromWorksheet(data, barcodeImage, qrCodeImage);
}

/**
 * 从Excel工作表生成HTML
 */
function generateHTMLFromWorksheet(
  worksheet: ExcelJS.Worksheet,
  data: DeliveryNoteData,
): string {
  const html = `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="UTF-8">
      <title>Delivery Note</title>
      <style>
        body {
          font-family: Arial, sans-serif;
          margin: 0;
          padding: 20px;
          background: white;
        }
        .delivery-note {
          max-width: 210mm;
          margin: 0 auto;
          background: white;
          padding: 20px;
        }
        .header {
          display: flex;
          align-items: center;
          justify-content: space-between;
          margin-bottom: 20px;
          width: 100%;
        }
        .logo-section {
          width: 150px;
          height: 80px;
          border: 1px solid #ccc;
          display: flex;
          align-items: center;
          justify-content: center;
          background: #f0f8ff;
          color: #0066cc;
          font-weight: bold;
          font-size: 12px;
          text-align: center;
          line-height: 1.2;
          position: relative;
          overflow: hidden;
        }
        .logo-section img {
          max-width: 100%;
          max-height: 100%;
          object-fit: contain;
        }
        .logo-section .logo-text {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
        }
        .title {
          font-size: 24px;
          font-weight: bold;
          text-align: center;
          flex: 1;
        }
        .serial {
          font-size: 16px;
          font-weight: bold;
        }
        .notice {
          text-align: center;
          color: #e53935;
          font-weight: bold;
          font-size: 11px;
          margin-bottom: 20px;
        }
        .main-table {
          width: 100%;
          border-collapse: collapse;
          margin-bottom: 20px;
        }
        .main-table td {
          border: 1px solid #000;
          padding: 8px;
          text-align: center;
          vertical-align: middle;
        }
        .label-cell {
          background: #f5f5f5;
          font-weight: bold;
          font-size: 10px;
        }
        .value-cell {
          font-size: 14px;
          font-weight: bold;
        }
        .barcode {
          font-family: 'Courier New', monospace;
          font-size: 10px;
          letter-spacing: 1px;
          background: white;
          padding: 2px;
          text-align: center;
          line-height: 1;
        }
        .barcode-container {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          height: 100%;
        }
        .barcode-lines {
          font-family: 'Courier New', monospace;
          font-size: 8px;
          letter-spacing: 0px;
          line-height: 0.8;
          transform: scaleX(0.5);
        }
        .shipping-method {
          font-size: 9px;
          line-height: 1.2;
        }
        .info-section {
          margin-top: 20px;
          font-size: 10px;
          line-height: 1.5;
        }
        .info-section div {
          border-bottom: 1px solid #000;
          padding: 5px 0;
          margin-bottom: 2px;
        }
        .big-mark {
          text-align: center;
          font-size: 72px;
          font-weight: bold;
          margin-top: 30px;
          line-height: 1;
        }
        @media print {
          body { margin: 0; padding: 0; }
          .delivery-note { max-width: none; padding: 10px; }
          @page { size: A4; margin: 10mm; }
        }
      </style>
    </head>
    <body>
      <div class="delivery-note">
        <!-- 头部 -->
        <div class="header">
          <div class="logo-section">
            ${
              data.logoUrl
                ? `<img src="${data.logoUrl}" alt="T&C Logo" />`
                : `<div class="logo-text">
                <div>T&C</div>
                <div>TOUCHTRANS &</div>
                <div>CLUB FREIGHT SERVICES</div>
              </div>`
            }
          </div>
          <div class="title">Delivery Note</div>
          <div class="serial">${data.serialNumber || '679'}</div>
        </div>

        <!-- 提示信息 -->
        <div class="notice">
          送货前请先联系 Sean 18188802770( 微信）填写此单贴在外包装上
        </div>

        <!-- 主要信息表格 -->
        <table class="main-table">
          <tr style="height: 40px;">
            <td class="label-cell" style="width: 80px;">唛头 Mark</td>
            <td class="value-cell" colspan="2" style="width: 200px;">${data.formData.markNumber || ''}</td>
            <td class="shipping-method" style="width: 100px;">${generateShippingMethodText(data.formData.shippingMethod).replaceAll('\n', '<br>')}</td>
            <td class="label-cell" style="width: 80px;">唛头条码</td>
            <td class="barcode" colspan="2">${generateBarcodeText(data.formData.markNumber || '')}</td>
          </tr>
          <tr style="height: 30px;">
            <td class="label-cell">品名 Item Name</td>
            <td class="value-cell" colspan="2">${data.formData.orderNo || ''}</td>
            <td class="label-cell">品名二维码</td>
            <td colspan="3" style="font-size: 8px;">■■■■■■■■■<br>■■■■■■■■■<br>■■■■■■■■■</td>
          </tr>
          <tr style="height: 30px;">
            <td class="label-cell">件数 PACKAGES</td>
            <td class="value-cell" colspan="2">${data.formData.quantity || ''}</td>
            <td colspan="4"></td>
          </tr>
        </table>

        <!-- 仓库信息 -->
        <div class="info-section">
          <div>1. Warehouse address 仓库地址：佛山顺德区乐从镇新隆村第一工业区内 101</div>
          <div style="text-align: center; margin: 5px 0;">Sean: +8618188802770</div>
          <div style="text-align: center; margin: 5px 0;">2. 上班时间：周一到周六，上午 10 点到下午 6 点。</div>
          <div style="text-align: center; margin: 5px 0;">3. 本仓库不收到付件，不收无唛头的件。</div>
          <div style="text-align: center; margin: 5px 0;">4. 仓库没有免费卸货服务，如需卸货，请联系仓库咨询费用。</div>
        </div>

        <!-- 大号唛头 -->
        <div class="big-mark">
          ${data.formData.markNumber || ''}
        </div>
      </div>
    </body>
    </html>
  `;

  return html;
}

/**
 * 下载Excel文件（已废弃）
 * @deprecated
 */
export function downloadExcelFile(buffer: ExcelJS.Buffer, filename: string) {
  const blob = new Blob([buffer], {
    type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  });

  const url = window.URL.createObjectURL(blob);
  const link = document.createElement('a');
  link.href = url;
  link.download = filename;
  document.body.append(link);
  link.click();
  link.remove();
  window.URL.revokeObjectURL(url);
}
