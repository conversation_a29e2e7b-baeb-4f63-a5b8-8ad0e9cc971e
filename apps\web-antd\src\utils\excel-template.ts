import { Buffer } from 'node:buffer';

import ExcelJS from 'exceljs';
// eslint-disable-next-line n/no-extraneous-import
import JsBarcode from 'jsbarcode';

// 定义数据接口
export interface DeliveryNoteData {
  serialNumber?: string;
  logoUrl?: string; // 动态logo URL
  formData: {
    markNumber?: string;
    orderNo?: string;
    quantity?: number;
    shippingMethod?: number | string;
  };
}

/**
 * 生成条形码文本表示
 */
function generateBarcodeText(text: string): string {
  // 简单的条形码文本表示
  return `|||${text
    // eslint-disable-next-line unicorn/prefer-spread
    .split('')
    .map(() => '||')
    .join('|')}|||`;
}

/**
 * 生成运输方式文本
 */
function generateShippingMethodText(shippingMethod?: number | string): string {
  const method =
    typeof shippingMethod === 'string'
      ? Number.parseInt(shippingMethod)
      : shippingMethod;
  if (method === 2) {
    return '●SEA海运\n○AIR空运';
  } else if (method === 1) {
    return '○SEA海运\n●AIR空运';
  } else {
    return '○SEA海运\n○AIR空运';
  }
}

/**
 * 生成条形码图片
 */
async function generateBarcodeImage(text: string): Promise<null | string> {
  if (!text) return null;

  try {
    const canvas = document.createElement('canvas');
    JsBarcode(canvas, text, {
      format: 'CODE128',
      width: 2,
      height: 50,
      displayValue: false,
      background: '#ffffff',
      lineColor: '#000000',
    });
    return canvas.toDataURL('image/png');
  } catch (error) {
    console.error('条形码生成失败:', error);
    return null;
  }
}

/**
 * 填充Excel工作表数据
 */
async function fillWorksheetData(
  worksheet: ExcelJS.Worksheet,
  data: DeliveryNoteData,
): Promise<void> {
  // 生成条形码图片
  const markNumberBarcode = await generateBarcodeImage(
    data.formData.markNumber || '',
  );
  const orderNoBarcode = await generateBarcodeImage(
    data.formData.orderNo || '',
  );

  // 定义占位符替换映射
  const replacements: Record<string, string> = {
    '{{ORDER_NO}}': data.formData.orderNo || '',
    '{{MARK_NUMBER}}': data.formData.markNumber || '',
    '{{SHIPPING_METHOD}}': generateShippingMethodText(
      data.formData.shippingMethod,
    ),
    '{{QUANTITY}}': (data.formData.quantity || 0).toString(),
    '{{BIG_MARK_NUMBER}}': data.formData.markNumber || '',
  };

  // 遍历所有单元格，替换占位符
  worksheet.eachRow((row) => {
    row.eachCell((cell) => {
      if (cell.value && typeof cell.value === 'string') {
        let cellValue = cell.value;

        // 替换所有占位符
        for (const [placeholder, replacement] of Object.entries(replacements)) {
          cellValue = cellValue.replaceAll(
            new RegExp(placeholder, 'g'),
            replacement,
          );
        }

        // 如果值发生了变化，更新单元格
        if (cellValue !== cell.value) {
          cell.value = cellValue;
        }
      }
    });
  });

  // 添加条形码图片（如果需要）
  if (markNumberBarcode) {
    await addBarcodeToWorksheet(worksheet, markNumberBarcode, 'markNumber');
  }

  if (orderNoBarcode) {
    await addBarcodeToWorksheet(worksheet, orderNoBarcode, 'orderNo');
  }

  // 添加logo（如果提供）
  if (data.logoUrl) {
    await addLogoToWorksheet(worksheet, data.logoUrl);
  }
}

/**
 * 向Excel工作表添加条形码图片
 */
async function addBarcodeToWorksheet(
  worksheet: ExcelJS.Worksheet,
  barcodeDataUrl: string,
  type: 'markNumber' | 'orderNo',
): Promise<void> {
  try {
    // 将base64数据转换为buffer
    const base64Data = barcodeDataUrl.split(',')[1];
    if (!base64Data) {
      console.error('无效的条形码数据URL');
      return;
    }
    const imageBuffer = Buffer.from(base64Data, 'base64') as any;

    // 添加图片到工作簿
    const imageId = worksheet.workbook.addImage({
      buffer: imageBuffer,
      extension: 'png',
    });

    // 根据类型确定插入位置
    let col: number, row: number;
    if (type === 'markNumber') {
      row = 10; // 唛头条形码位置
      col = 5;
    } else {
      row = 15; // 品名条形码位置
      col = 5;
    }

    // 插入图片
    worksheet.addImage(imageId, {
      tl: { col: col - 1, row: row - 1 },
      ext: { width: 200, height: 50 },
    });
  } catch (error) {
    console.error('添加条形码图片失败:', error);
  }
}

/**
 * 向Excel工作表添加Logo图片
 */
async function addLogoToWorksheet(
  worksheet: ExcelJS.Worksheet,
  logoUrl: string,
): Promise<void> {
  try {
    // 如果是本地文件路径，需要读取文件
    let imageBuffer: Buffer;

    if (logoUrl.startsWith('data:')) {
      // base64格式
      const base64Data = logoUrl.split(',')[1];
      if (!base64Data) {
        console.error('无效的Logo数据URL');
        return;
      }
      imageBuffer = Buffer.from(base64Data, 'base64') as any;
    } else {
      // 文件路径或URL，这里简化处理
      console.warn('Logo URL处理需要根据实际情况实现:', logoUrl);
      return;
    }

    // 添加图片到工作簿
    const imageId = worksheet.workbook.addImage({
      buffer: imageBuffer as any,
      extension: 'png',
    });

    // 插入Logo到指定位置（居中）
    worksheet.addImage(imageId, {
      tl: { col: 3, row: 1 }, // 居中位置
      ext: { width: 100, height: 60 },
    });
  } catch (error) {
    console.error('添加Logo图片失败:', error);
  }
}

/**
 * 使用模板填充数据生成Excel文件
 */
export async function fillDeliveryNoteTemplate(
  data: DeliveryNoteData,
): Promise<ExcelJS.Buffer> {
  // 加载模板文件
  const templatePath = '/templates/delivery-note-template.xlsx';
  const response = await fetch(templatePath);

  if (!response.ok) {
    throw new Error(`无法加载模板文件: ${templatePath}`);
  }

  const arrayBuffer = await response.arrayBuffer();
  const workbook = new ExcelJS.Workbook();
  await workbook.xlsx.load(arrayBuffer);

  const worksheet = workbook.getWorksheet('Delivery Note');
  if (!worksheet) {
    throw new Error('模板文件中未找到 "Delivery Note" 工作表');
  }

  // 替换占位符数据
  const replacements = {
    '{{MARK_NUMBER}}': data.formData.markNumber || '',
    '{{SHIPPING_METHOD}}': generateShippingMethodText(
      data.formData.shippingMethod,
    ),
    '{{BARCODE}}': generateBarcodeText(data.formData.markNumber || ''),
    '{{ORDER_NO}}': data.formData.orderNo || '',
    '{{QR_CODE}}': '■■■■■■■■■\n■■■■■■■■■\n■■■■■■■■■',
    '{{QUANTITY}}': (data.formData.quantity || 0).toString(),
    '{{BIG_MARK_NUMBER}}': data.formData.markNumber || '',
  };

  // 遍历所有单元格，替换占位符
  worksheet.eachRow((row) => {
    row.eachCell((cell) => {
      if (cell.value && typeof cell.value === 'string') {
        let cellValue = cell.value;
        for (const [placeholder, replacement] of Object.entries(replacements)) {
          if (cellValue.includes(placeholder)) {
            cellValue = cellValue.replace(placeholder, replacement);
          }
        }
        if (cellValue !== cell.value) {
          cell.value = cellValue;
        }
      }
    });
  });

  return workbook.xlsx.writeBuffer() as Promise<ExcelJS.Buffer>;
}

/**
 * 为了向后兼容保留的函数
 */
export async function createDeliveryNoteTemplate(
  data: DeliveryNoteData,
): Promise<ExcelJS.Buffer> {
  return fillDeliveryNoteTemplate(data);
}

/**
 * 将填充数据的Excel模板转换为HTML格式
 */
/**
 * 直接填充Excel模板数据并返回工作簿
 */
export async function fillExcelTemplate(
  data: DeliveryNoteData,
): Promise<ExcelJS.Workbook> {
  // 加载Excel模板
  const workbook = new ExcelJS.Workbook();
  await workbook.xlsx.readFile('/templates/delivery-note-template.xlsx');

  const worksheet = workbook.getWorksheet(1);
  if (!worksheet) {
    throw new Error('Excel模板工作表不存在');
  }

  // 填充数据到Excel模板
  await fillWorksheetData(worksheet, data);

  return workbook;
}

/**
 * 填充Excel模板数据并生成可打印的Excel文件
 */
export async function generatePrintableExcel(
  data: DeliveryNoteData,
): Promise<ArrayBuffer> {
  const workbook = await fillExcelTemplate(data);

  // 返回Excel文件的Buffer
  return await workbook.xlsx.writeBuffer();
}

/**
 * 生成PDF文件（通过Excel转换）
 */
export async function generatePrintablePDF(
  data: DeliveryNoteData,
): Promise<ArrayBuffer> {
  // 先生成Excel
  const workbook = await fillExcelTemplate(data);

  // 方法1: 返回Excel，让用户在Excel中转PDF（推荐）
  return await workbook.xlsx.writeBuffer();

  // 方法2: 如果需要服务端转换，可以调用服务端API
  // const excelBuffer = await workbook.xlsx.writeBuffer();
  // return await convertExcelToPDFOnServer(excelBuffer);
}

/**
 * 保持HTML转换功能（向后兼容）
 */
export async function convertExcelTemplateToHTML(
  data: DeliveryNoteData,
): Promise<string> {
  // 生成条形码图片
  const barcodeImage = await generateBarcodeImage(
    data.formData.markNumber || '',
  );
  const orderBarcodeImage = await generateBarcodeImage(
    data.formData.orderNo || '',
  );

  // 转换为HTML
  return generateHTMLFromWorksheet(data, barcodeImage, orderBarcodeImage);
}

/**
 * 从Excel工作表生成HTML
 */
function generateHTMLFromWorksheet(
  data: DeliveryNoteData,
  barcodeImage: null | string,
  orderBarcodeImage: null | string,
): string {
  const html = `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="UTF-8">
      <title>Delivery Note</title>
      <style>
        body {
          font-family: Arial, sans-serif;
          margin: 0;
          padding: 20px;
          background: white;
        }
        .delivery-note {
          max-width: 210mm;
          margin: 0 auto;
          background: white;
          padding: 20px;
        }
        .header {
          display: flex;
          flex-direction: column;
          align-items: center;
          margin-bottom: 20px;
          width: 100%;
        }
        .logo-section {
          width: 150px;
          height: 80px;
          border: 1px solid #ccc;
          display: flex;
          align-items: center;
          justify-content: center;
          background: #f0f8ff;
          color: #0066cc;
          font-weight: bold;
          font-size: 12px;
          text-align: center;
          line-height: 1.2;
          position: relative;
          overflow: hidden;
        }
        .logo-section img {
          max-width: 100%;
          max-height: 100%;
          object-fit: contain;
        }
        .logo-section .logo-text {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
        }
        .title {
          font-size: 24px;
          font-weight: bold;
          text-align: center;
          flex: 1;
        }
        .serial {
          font-size: 16px;
          font-weight: bold;
        }
        .notice {
          text-align: center;
          color: #e53935;
          font-weight: bold;
          font-size: 11px;
          margin-bottom: 20px;
        }
        .main-table {
          width: 100%;
          border-collapse: collapse;
          margin-bottom: 20px;
        }
        .main-table td {
          border: 1px solid #000;
          padding: 8px;
          text-align: center;
          vertical-align: middle;
        }
        .label-cell {
          background: #f5f5f5;
          font-weight: bold;
          font-size: 10px;
        }
        .value-cell {
          font-size: 14px;
          font-weight: bold;
        }
        .barcode {
          font-family: 'Courier New', monospace;
          font-size: 10px;
          letter-spacing: 1px;
          background: white;
          padding: 2px;
          text-align: center;
          line-height: 1;
        }
        .barcode-container {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          height: 100%;
        }
        .barcode-lines {
          font-family: 'Courier New', monospace;
          font-size: 8px;
          letter-spacing: 0px;
          line-height: 0.8;
          transform: scaleX(0.5);
        }
        .shipping-method {
          font-size: 9px;
          line-height: 1.2;
        }
        .info-section {
          margin-top: 20px;
          font-size: 10px;
          line-height: 1.5;
        }
        .info-section div {
          border-bottom: 1px solid #000;
          padding: 5px 0;
          margin-bottom: 2px;
        }
        .big-mark {
          text-align: center;
          font-size: 72px;
          font-weight: bold;
          margin-top: 30px;
          line-height: 1;
        }
        .signature-section {
          margin-top: 40px;
          padding: 20px 0;
        }
        .signature-row {
          display: flex;
          justify-content: space-between;
          align-items: center;
          gap: 40px;
        }
        .signature-item {
          display: flex;
          align-items: center;
          gap: 10px;
          font-size: 16px;
        }
        .signature-line {
          display: inline-block;
          width: 120px;
          height: 1px;
          border-bottom: 1px solid #000;
          margin-left: 5px;
        }
        @media print {
          body {
            margin: 0;
            padding: 0;
            -webkit-print-color-adjust: exact !important;
            color-adjust: exact !important;
            print-color-adjust: exact !important;
          }
          .delivery-note {
            max-width: none;
            padding: 10px;
            -webkit-print-color-adjust: exact !important;
            color-adjust: exact !important;
            print-color-adjust: exact !important;
          }
          img {
            -webkit-print-color-adjust: exact !important;
            color-adjust: exact !important;
            print-color-adjust: exact !important;
          }
          @page {
            size: A4;
            margin: 10mm;
          }
        }
      </style>
    </head>
    <body>
      <div class="delivery-note">
        <!-- 头部 -->
        <div class="header">
          <div class="logo-section">
            ${
              data.logoUrl
                ? `<img src="${data.logoUrl}" alt="Company Logo" style="height: 60px; margin-bottom: 10px;" />`
                : `<div class="logo-text">
                <div>T&C</div>
                <div>TOUCHTRANS &</div>
                <div>CLUB FREIGHT SERVICES</div>
              </div>`
            }
          </div>
          <div class="title">物流入仓单</div>
          <div class="serial">SERIAL_NUMBER: ${data.serialNumber || data.formData.orderNo || '679'}</div>
        </div>

        <!-- 提示信息 -->
        <div class="notice">
          送货前请先联系 Sean 18188802770( 微信）填写此单贴在外包装上
        </div>

        <!-- 主要信息表格 -->
        <table class="main-table">
          <tr style="height: 40px;">
            <td class="label-cell" style="width: 80px;">唛头 Mark</td>
            <td class="value-cell" colspan="2" style="width: 200px;">${data.formData.markNumber || ''}</td>
            <td class="shipping-method" style="width: 100px;">${generateShippingMethodText(data.formData.shippingMethod).replaceAll('\n', '<br>')}</td>
            <td class="label-cell" style="width: 80px;">唛头条码</td>
            <td class="barcode" colspan="2">
              ${
                barcodeImage
                  ? `<img src="${barcodeImage}" alt="Barcode" style="max-width: 100%; height: 50px;" />`
                  : generateBarcodeText(data.formData.markNumber || '')
              }
            </td>
          </tr>
          <tr style="height: 30px;">
            <td class="label-cell">品名 Item Name</td>
            <td class="value-cell" colspan="2">${data.formData.orderNo || ''}</td>
            <td class="label-cell">品名条码</td>
            <td colspan="3" style="text-align: center;">
              ${
                orderBarcodeImage
                  ? `<img src="${orderBarcodeImage}" alt="Order Barcode" style="max-width: 100%; height: 50px;" />`
                  : generateBarcodeText(data.formData.orderNo || '')
              }
            </td>
          </tr>
          <tr style="height: 30px;">
            <td class="label-cell">件数 PACKAGES</td>
            <td class="value-cell" colspan="2">${data.formData.quantity || ''}</td>
            <td colspan="4"></td>
          </tr>
        </table>

        <!-- 仓库信息 -->
        <div class="info-section">
          <div>1. Warehouse address 仓库地址：佛山顺德区乐从镇新隆村第一工业区内 101</div>
          <div style="text-align: center; margin: 5px 0;">Sean: +8618188802770</div>
          <div style="text-align: center; margin: 5px 0;">2. 上班时间：周一到周六，上午 10 点到下午 6 点。</div>
          <div style="text-align: center; margin: 5px 0;">3. 本仓库不收到付件，不收无唛头的件。</div>
          <div style="text-align: center; margin: 5px 0;">4. 仓库没有免费卸货服务，如需卸货，请联系仓库咨询费用。</div>
        </div>

        <!-- 大号唛头 -->
        <div class="big-mark">
          ${data.formData.markNumber || ''}
        </div>

        <!-- 签字栏 -->
        <div class="signature-section">
          <div class="signature-row">
            <div class="signature-item">
              <span>经办人签字：</span>
              <span class="signature-line"></span>
            </div>
            <div class="signature-item">
              <span>收货人签字：</span>
              <span class="signature-line"></span>
            </div>
            <div class="signature-item">
              <span>日期：</span>
              <span class="signature-line"></span>
            </div>
          </div>
        </div>
      </div>
    </body>
    </html>
  `;

  return html;
}

/**
 * 下载Excel文件（已废弃）
 * @deprecated
 */
export function downloadExcelFile(buffer: ExcelJS.Buffer, filename: string) {
  const blob = new Blob([buffer], {
    type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  });

  const url = window.URL.createObjectURL(blob);
  const link = document.createElement('a');
  link.href = url;
  link.download = filename;
  document.body.append(link);
  link.click();
  link.remove();
  window.URL.revokeObjectURL(url);
}
