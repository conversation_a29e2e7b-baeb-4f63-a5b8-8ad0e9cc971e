<script setup lang="ts">
import { nextTick, onMounted, ref } from 'vue';

// eslint-disable-next-line n/no-extraneous-import
import JsBarcode from 'jsbarcode';

const props = defineProps<{
  logo?: string;
  orderInfo: Record<string, any>;
  orderItems: any[];
}>();

const barcodeRef = ref<HTMLCanvasElement>();

function getShippingMethod(val: any) {
  if (val === 1) return '空运';
  if (val === 2) return '海运';
  if (val === 3) return '陆运';
  return val || '-';
}

// 生成条形码
function generateBarcode() {
  if (!props.orderInfo.trackingNumber || !barcodeRef.value) return;

  try {
    JsBarcode(barcodeRef.value, props.orderInfo.trackingNumber, {
      format: 'CODE128',
      width: 2,
      height: 50,
      displayValue: false,
      background: '#ffffff',
      lineColor: '#000000',
    });
  } catch (error) {
    console.error('条形码生成失败:', error);
  }
}

onMounted(() => {
  nextTick(() => {
    generateBarcode();
  });
});
</script>

<template>
  <div
    class="print-template m-0 w-full p-0"
    style="width: 210mm; min-height: 297mm; font-size: 15px"
  >
    <!-- 标题和logo -->
    <div class="mb-6 text-center">
      <img v-if="logo" :src="logo" alt="公司Logo" class="mx-auto mb-4 h-16" />
      <div class="text-3xl font-bold">物流入仓单</div>
    </div>
    <!-- 主表信息双列 -->
    <div class="mb-6 grid grid-cols-2 gap-x-8 text-base">
      <div>
        <div class="mb-2">单号{{ orderInfo.orderNo }}</div>
        <div class="mb-2">发货方：{{ orderInfo.supplierName }}</div>
        <div class="mb-2">发货电话：{{ orderInfo.supplierTel }}</div>
        <div class="mb-2">收货方：{{ orderInfo.customerName }}</div>
        <div class="mb-2">收货电话：{{ orderInfo.customerTel }}</div>
      </div>
      <div>
        <div class="mb-2">日期：{{ orderInfo.createTime || '-' }}</div>
        <div class="mb-2">
          运输方式：{{ getShippingMethod(orderInfo.shippingMethod) }}
        </div>
        <div class="mb-2">入库仓库：{{ orderInfo.warehouse }}</div>
        <div class="mb-2">唛头：{{ orderInfo.markNumber }}</div>
        <div class="mb-2">
          物流条码：
          <div v-if="orderInfo.trackingNumber" class="mt-1">
            <canvas ref="barcodeRef" class="border"></canvas>
            <div class="text-center text-sm">
              {{ orderInfo.trackingNumber }}
            </div>
          </div>
          <span v-else>-</span>
        </div>
      </div>
    </div>
    <!-- 明细表格 -->
    <table class="mb-6 w-full border-collapse border text-base">
      <thead>
        <tr class="bg-gray-100">
          <th class="w-12 border p-1">序号</th>
          <th class="border p-1">商品名称</th>
          <th class="border p-1">数量</th>
          <th class="border p-1">备注</th>
        </tr>
      </thead>
      <tbody>
        <tr v-for="(item, idx) in orderItems" :key="idx">
          <td class="border p-1 text-center">{{ idx + 1 }}</td>
          <td class="border p-1">{{ item.itemName }}</td>
          <td class="border p-1 text-center">{{ item.quantity }}</td>
          <td class="border p-1"></td>
        </tr>
      </tbody>
    </table>
    <!-- 签字栏 -->
    <div class="mt-12 flex justify-between text-base">
      <div class="flex items-center">
        <span class="mr-2">经办人签字：</span>
        <span class="inline-block w-32 border-b border-black pb-1"></span>
      </div>
      <div class="flex items-center">
        <span class="mr-2">收货人签字：</span>
        <span class="inline-block w-32 border-b border-black pb-1"></span>
      </div>
      <div class="flex items-center">
        <span class="mr-2">日期：</span>
        <span class="inline-block w-32 border-b border-black pb-1"></span>
      </div>
    </div>
  </div>
</template>

<style scoped>
.print-template {
  font-family: 'Microsoft YaHei', Arial, sans-serif;
}
@media print {
  .print-template {
    box-shadow: none !important;
    border: none !important;
    background: white !important;
    margin: 0 !important;
    padding: 0 !important;
    width: 210mm !important;
    min-width: 210mm !important;
    max-width: 210mm !important;
    min-height: 297mm !important;
    max-height: 297mm !important;
    font-size: 15px !important;
  }
}
</style>
