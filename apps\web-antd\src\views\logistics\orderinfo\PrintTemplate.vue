<script setup lang="ts">
const props = defineProps<{
  logo?: string;
  orderInfo: Record<string, any>;
  orderItems: any[];
}>();

function getShippingMethod(val: any) {
  if (val === 1) return '空运';
  if (val === 2) return '海运';
  if (val === 3) return '陆运';
  return val || '-';
}
</script>

<template>
  <div
    class="print-template m-0 w-full p-0"
    style="width: 210mm; min-height: 297mm; font-size: 15px"
  >
    <!-- 标题和logo -->
    <div class="mb-4 flex items-center justify-between">
      <img v-if="logo" :src="logo" alt="公司Logo" class="h-12" />
      <div class="flex-1 text-center text-2xl font-bold">物流入仓单</div>
      <div class="w-12"></div>
    </div>
    <!-- 主表信息双列 -->
    <div class="mb-4 grid grid-cols-2 gap-x-8 text-base">
      <div>
        <div>单号：{{ orderInfo.orderNo }}</div>
        <div>发货方：{{ orderInfo.supplierName }}</div>
        <div>发货电话：{{ orderInfo.supplierTel }}</div>
        <div>收货方：{{ orderInfo.customerName }}</div>
        <div>收货电话：{{ orderInfo.customerTel }}</div>
      </div>
      <div>
        <div>日期：{{ orderInfo.createTime || '-' }}</div>
        <div>运输方式：{{ getShippingMethod(orderInfo.shippingMethod) }}</div>
        <div>入库仓库：{{ orderInfo.warehouse }}</div>
        <div>唛头：{{ orderInfo.markNumber }}</div>
        <div>物流条码：{{ orderInfo.trackingNumber }}</div>
      </div>
    </div>
    <!-- 明细表格 -->
    <table class="mb-6 w-full border-collapse border text-base">
      <thead>
        <tr class="bg-gray-100">
          <th class="w-12 border p-1">序号</th>
          <th class="border p-1">商品名称</th>
          <th class="border p-1">数量</th>
          <th class="border p-1">备注</th>
        </tr>
      </thead>
      <tbody>
        <tr v-for="(item, idx) in orderItems" :key="idx">
          <td class="border p-1 text-center">{{ idx + 1 }}</td>
          <td class="border p-1">{{ item.itemName }}</td>
          <td class="border p-1 text-center">{{ item.quantity }}</td>
          <td class="border p-1"></td>
        </tr>
      </tbody>
    </table>
    <!-- 签字栏 -->
    <div class="mt-8 flex justify-between">
      <div>经办人签字：</div>
      <div>收货人签字：</div>
      <div>日期：</div>
    </div>
  </div>
</template>

<style scoped>
.print-template {
  font-family: 'Microsoft YaHei', Arial, sans-serif;
}
@media print {
  .print-template {
    box-shadow: none !important;
    border: none !important;
    background: white !important;
    margin: 0 !important;
    padding: 0 !important;
    width: 210mm !important;
    min-width: 210mm !important;
    max-width: 210mm !important;
    min-height: 297mm !important;
    max-height: 297mm !important;
    font-size: 15px !important;
  }
}
</style>
