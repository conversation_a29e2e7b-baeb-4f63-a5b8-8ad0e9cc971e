import { convertExcelTemplateToHTML } from '../src/utils/excel-template.js';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

/**
 * 测试修复后的打印模板功能
 */
async function testPrintTemplate() {
  try {
    console.log('🧪 开始测试修复后的打印模板功能...');

    // 测试数据
    const testData = {
      serialNumber: 'TEST001',
      logoUrl: '/logo.png', // 动态logo URL
      formData: {
        orderNo: 'ORD20241230001',
        markNumber: 'MARK123456',
        quantity: 10,
        shippingMethod: 1, // 1=空运, 2=海运
      },
    };

    console.log('📝 测试数据:', JSON.stringify(testData, null, 2));

    // 生成HTML
    const htmlContent = await convertExcelTemplateToHTML(testData);
    
    console.log('✅ HTML生成成功');

    // 保存测试文件
    const outputPath = path.join(__dirname, '../public/test-print-template.html');
    fs.writeFileSync(outputPath, htmlContent, 'utf8');
    
    console.log('✅ 测试HTML文件已生成:', outputPath);
    console.log('🎉 打印模板修复测试成功！');
    console.log('📝 可以在浏览器中打开测试文件查看效果');
    console.log('');
    console.log('修复内容包括:');
    console.log('✅ Logo居中显示');
    console.log('✅ 条形码按图片显示并动态设置');
    console.log('✅ SERIAL_NUMBER正确显示');
    console.log('✅ 签字栏添加下划线');

  } catch (error) {
    console.error('❌ 测试失败:', error);
  }
}

// 运行测试
testPrintTemplate();
